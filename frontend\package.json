{"name": "realtime-order-management-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "lucide-react": "^0.294.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "socket.io-client": "^4.7.4"}, "devDependencies": {"@eslint/js": "^9.29.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^30.0.0", "@types/node": "^20.10.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "@vitest/ui": "^1.0.4", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "jsdom": "^23.0.1", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vitest": "^1.0.4"}}